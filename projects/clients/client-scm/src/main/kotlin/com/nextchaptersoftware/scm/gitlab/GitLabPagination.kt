package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.HttpClientPagination.batchStream
import com.nextchaptersoftware.ktor.client.LinkHeaders
import com.nextchaptersoftware.pagination.asFlatItemsFlow
import io.ktor.client.HttpClient
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.parameter
import kotlin.math.min
import kotlinx.coroutines.flow.Flow

/**
 * Wraps our base HttpClient pagination to force use of GitLab's
 * keyset pagination and custom page size.
 *
 * See [com.nextchaptersoftware.ktor.client.HttpClientPagination].
 */
internal object GitLabPagination {

    inline fun <reified T> HttpClient.gitLabStream(
        initialUri: String,
        maxItems: Int? = null,
        useKeysetPagination: Boolean = true,
        noinline block: HttpRequestBuilder.() -> Unit = {},
    ): Flow<T> {
        return gitLabBatchStream<T>(
            initialUri = initialUri,
            maxItems = maxItems,
            useKeysetPagination = useKeysetPagination,
            block = block,
        )
        .asFlatItemsFlow()
    }

    inline fun <reified T> HttpClient.gitLabBatchStream(
        initialUri: String,
        maxItems: Int? = null,
        useKeysetPagination: Boolean = true,
        noinline block: HttpRequestBuilder.() -> Unit = {},
    ): Flow<HttpClientBatch<T>> {
        return batchStream(
            initialUri = initialUri,
            maxItems = maxItems,
            nextUriProvider = { LinkHeaders.getRelation(it, "next") },
            block = {
                if (useKeysetPagination) {
                    parameter("pagination", "keyset")
                }
                parameter("per_page", pageSize(maxItems))
                block()
            },
        )
    }

    /**
     * Default API page size. GitLab defaults to 20, and maximum allowed is 100.
     * GitLab 500s for some endpoints (eg: `diffs`) when using more than 30.
     * See [GitLab Pagination](https://docs.gitlab.com/ee/api/rest/#pagination).
     */
    @Suppress("MagicNumber")
    private val defaultPageSize = 30

    private fun pageSize(maxItems: Int?) = maxItems?.let { min(it, defaultPageSize) } ?: defaultPageSize
}
