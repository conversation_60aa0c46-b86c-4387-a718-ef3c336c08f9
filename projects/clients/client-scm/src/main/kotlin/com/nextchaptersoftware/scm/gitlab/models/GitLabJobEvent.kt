package com.nextchaptersoftware.scm.gitlab.models

import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GitLabJobEvent(
    @SerialName("build_id")
    val id: Long,

    @SerialName("pipeline_id")
    val pipelineId: Long,

    val project: GitLabProject,

    val ref: String,
    val sha: String,
    @SerialName("before_sha")
    val beforeSha: String,

    @SerialName("build_name")
    val name: String,

    @SerialName("build_status")
    val status: GitLabStatus,

    @SerialName("build_created_at_iso")
    val createdAt: Instant,

    @SerialName("build_started_at_iso")
    val startedAt: Instant? = null,

    @SerialName("build_finished_at_iso")
    val finishedAt: Instant? = null,

    @SerialName("retries_count")
    val retriesCount: Int,
)
