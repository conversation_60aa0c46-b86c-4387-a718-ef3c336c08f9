package com.nextchaptersoftware.scm.gitlab.models

import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GitLabPipeline(
    val id: Long,
    val iid: Int,

    val status: GitLabStatus,
    val url: String,
    val ref: String,
    val sha: String,

    @SerialName("before_sha")
    val beforeSha: String,

    @Serializable(with = GitLabInstantSerializer::class)
    @SerialName("created_at")
    val createdAt: Instant,

    @Serializable(with = GitLabInstantSerializer::class)
    @SerialName("finished_at")
    val finishedAt: Instant? = null,

    val duration: Long? = null,
)
