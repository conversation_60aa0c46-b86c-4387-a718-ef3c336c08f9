package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitLabPipelineEventTest {

    private fun test(
        file: String,
        test: (GitLabPipelineEvent) -> Unit = {},
    ) {
        testData(
            file = file,
            decoder = { it.decode<GitLabPipelineEvent>() },
        ) {
            assertThat(it)
                .isNotNull
                .satisfies(test)
        }
    }

    @Test
    fun `pipeline-1 -- created`() = test("/scm/gitlab/webhook-pipeline-1-pending.json") {
        assertThat(it.pipeline.createdAt).isEqualTo(
            Instant.parse("2025-06-09T20:30:59Z"),
        )
    }

    @Test
    fun `pipeline-2 -- running`() = test("/scm/gitlab/webhook-pipeline-2-running.json")

    @Test
    fun `pipeline-3 -- success`() = test("/scm/gitlab/webhook-pipeline-3-success.json")

    @Test
    fun `pipeline-4 -- failed`() = test("/scm/gitlab/webhook-pipeline-4-failed.json")

    @Test
    fun `pipeline-5 -- cancelling`() = test("/scm/gitlab/webhook-pipeline-5-cancelling.json.gz")
}
