authRemoteEndpoint = "https://tunnel.secops.getunblocked.com:4093"
webRemoteEndpoint = "https://tunnel.secops.getunblocked.com:4000"
//authRemoteEndpoint = "https://richie.ngrok.io"

authentication {
    authTokenExpiry: 30m
}

test {
    maxTestRetries: 0
}

internalSlack {
    fallbackChannel: "test-richie"
}

providers {
    asana {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/asana/exchange"
        }
    }
    confluence {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/confluence/exchange"
        }
    }
    googleDrive {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/google/exchange"
        }
    }
    jira {
        // LOCAL richie app [https://developer.atlassian.com/console/myapps/************************62eb1933a4eb/overview]
        appId: "************************62eb1933a4eb"
        baseUrlOverride: ${authRemoteEndpoint}
        oauth {
            clientId: "wcYftYLNmHnVP5Y5XbZyxoIrJWym38g5"
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/jira/exchange"
        }
    }
    linear {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/linear/exchange"
        }
    }
    notion {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/notion/exchange"
        }
    }
    slack {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/slack/exchange"
        }
        openId {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/slack/exchange"
        }
        openIdSignIn {
            oauthCallbackUrl: ${webRemoteEndpoint}"/login/exchange"
        }
    }
    slackUAT {
        oauth {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/slack/exchange"
        }
        openId {
            oauthCallbackUrl: ${authRemoteEndpoint}"/api/auth/slack/exchange"
        }
        openIdSignIn {
            oauthCallbackUrl: ${webRemoteEndpoint}"/login/exchange"
        }
    }
}
